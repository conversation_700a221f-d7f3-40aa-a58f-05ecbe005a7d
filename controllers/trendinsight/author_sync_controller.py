"""
TrendInsight 作者同步控制器

负责同步作者和其视频列表相关的业务逻辑，集成收件箱关联处理功能
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

from fastapi import HTTPException

from mappers.trendinsight import TrendInsightVideoMapper
from models.enums import AuthorActionType, Platform, SourceType
from models.qihaozhushou import UserInboxSourceRelated, SourceType as QihaoSourceType
from models.trendinsight.models import (
    TrendInsightAuthor,
    TrendInsightVideoRelated,
    update_trendinsight_author,
)
from services.trendinsight import DouyinAwemeService
from rpc.trendinsight import AsyncTrendInsightAPI, client_manager
from rpc.trendinsight.config import TrendInsightConfig
from rpc.trendinsight.schemas import AuthorDetailResponse, GreatUserTopVideoRequest
from schemas.trendinsight import AuthorData, AuthorSyncResponse, AuthorUpdateData, DouyinAwemeData
from services.base import BaseService, ValidationError
from services.inbox import InboxService
from services.inbox.inbox_relation_service import InboxRelationService


class AuthorSyncController(BaseService):
    """TrendInsight 作者同步控制器 - 集成收件箱关联处理功能"""

    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化作者同步控制器

        Args:
            logger: 日志记录器，如果不提供则使用默认配置
        """
        super().__init__(logger)
        self.config = TrendInsightConfig()
        self.inbox_service = InboxService(self.logger)
        self.inbox_relation_service = InboxRelationService(self.logger)

    async def _get_trendinsight_client(self) -> AsyncTrendInsightAPI:
        """
        获取 TrendInsight 异步客户端实例

        Returns:
            AsyncTrendInsightAPI: 配置好账号提供者的异步客户端实例
        """
        # 通过 client_manager 创建客户端，cookies 由账号提供者自动管理
        async_client = client_manager.create_async_client()
        return AsyncTrendInsightAPI(async_client=async_client)

    async def get_author_detail(self, user_id: str) -> AuthorDetailResponse:
        """
        获取作者详情接口

        Args:
            user_id: 用户ID

        Returns:
            AuthorDetailResponse: 作者详情响应
        """
        try:
            client = await self._get_trendinsight_client()
            response = await client.get_author_detail(user_id)
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取作者详情失败: {str(e)}")

    async def sync_author_videos(
        self,
        user_id: str,
        enable_inbox_relation: bool = True
    ) -> AuthorSyncResponse:
        """
        同步作者和其视频列表

        功能：
        1. 通过 TrendInsight API 获取作者详情，不存在则创建，存在则使用现有记录
        2. 通过 TrendInsight API 获取用户热门视频列表（最近30天）
        3. 在 trendinsight_video_related 表中创建关联记录（不存在才创建）
        4. 智能批量处理 DouyinAweme 表记录（创建和更新）
        5. 可选：自动为所有订阅该作者的用户处理收件箱关联

        Args:
            user_id: TrendInsight 用户ID
            enable_inbox_relation: 是否启用收件箱关联处理（默认True）

        Returns:
            AuthorSyncResponse: 同步结果信息

        Example:
            {
                "author_action": "created",
                "author_data": {
                    "user_id": "heicgcbajggjdjjaefj",
                    "user_name": "科技博主",
                    "douyin_user_id": "MS4wLjABAAAA123",
                },
                "videos_synced": 15,
                "videos_failed": 0,
                "relations_created": 15,
                "relations_existing": 0,
                "aweme_ids": ["7123456789012345678", "7123456789012345679"],
                "video_items": [
                    {
                        "aweme_id": "7123456789012345678",
                        "title": "科技前沿视频",
                        "desc": "这是一个关于科技前沿的视频",
                        "create_time": 1705314645,
                        "nickname": "科技博主",
                        "liked_count": "1000",
                        "comment_count": "50",
                        "share_count": "20",
                        "collected_count": "30",
                        "source_keyword": "author_sync_heicgcbajggjdjjaefj"
                    }
                ],
                "errors": []
            }
        """
        try:
            # 初始化 AuthorSyncResponse 实例，提供类型安全性
            sync_result = AuthorSyncResponse(
                author_action=AuthorActionType.EXISTING,  # 默认值，后续会更新
                author_data=None,
                videos_synced=0,
                videos_failed=0,
                relations_created=0,
                relations_existing=0,
                aweme_ids=[],
                video_items=[],
                errors=[],
            )

            # 1. 检查作者是否已存在
            existing_author = await TrendInsightAuthor.filter(user_id=user_id).first()

            if existing_author:
                sync_result.author_action = AuthorActionType.EXISTING
                sync_result.author_data = AuthorData.model_validate(existing_author)
                self.logger.info(f"作者已存在: {user_id}")
            else:
                # 2. 通过 TrendInsight API 获取作者详情
                try:
                    self.logger.info(f"开始获取作者详情: {user_id}")
                    author_detail = await self.get_author_detail(user_id)

                    if author_detail.is_success and author_detail.author_detail:
                        author_data = author_detail.author_detail

                        # 3. 转换并保存作者数据 - 使用强类型数据模型
                        author_update_data = AuthorUpdateData.from_author_detail(
                            author_detail=author_data, user_id=user_id
                        )

                        # 作者创建时将 item_count 和 item_count_int 初始化为 0
                        author_dict = author_update_data.to_dict()
                        author_dict["item_count"] = "0"  # 初始化为 0，后续会根据实际同步结果更新

                        success = await update_trendinsight_author(author_dict)
                        if success:
                            sync_result.author_action = AuthorActionType.CREATED
                            # 获取刚创建的作者记录
                            created_author = await TrendInsightAuthor.filter(user_id=user_id).first()
                            if created_author:
                                sync_result.author_data = AuthorData.model_validate(created_author)
                            self.logger.info(f"作者创建成功: {user_id}")
                        else:
                            error_msg: str = f"保存作者数据失败: {user_id}"
                            sync_result.errors.append(error_msg)
                            self.logger.error(error_msg)
                            return sync_result
                    else:
                        error_msg: str = f"获取作者详情失败: {user_id}"
                        sync_result.errors.append(error_msg)
                        self.logger.error(error_msg)
                        return sync_result

                except Exception as e:
                    error_msg: str = f"获取或创建作者失败: {str(e)}"
                    sync_result.errors.append(error_msg)
                    self.logger.error(error_msg)
                    return sync_result

            # 4. 通过 TrendInsight RPC 服务获取用户热门视频列表
            try:
                # 使用 TrendInsight API 获取用户热门视频
                trendinsight_client = await self._get_trendinsight_client()

                # 构建日期范围（获取最近30天的视频）
                end_date = datetime.now()
                start_date = end_date - timedelta(days=30)

                # 构建 TrendInsight 用户热门视频请求
                video_request = GreatUserTopVideoRequest(
                    user_id=user_id,  # 使用 TrendInsight 用户ID
                    start_date=start_date.strftime("%Y%m%d"),
                    end_date=end_date.strftime("%Y%m%d"),
                )

                video_response = await trendinsight_client.get_great_user_top_video(video_request)

                if video_response.is_success and video_response.video_data:
                    # 6. 批量检查和创建视频关联记录
                    video_data = video_response.video_data

                    # 只取指数排序的视频列表（内容都是一样的，取一个就够了）
                    video_list = video_data.index_list or []

                    # 使用映射器将 TrendInsight TopVideoInfo 数据转换为完整的 DouyinAwemeData 格式
                    video_data_list, video_ids = TrendInsightVideoMapper.top_video_list_to_douyin_aweme_data_list(
                        video_list=video_list, source_keyword=f"author_sync_{user_id}"
                    )

                    # 将完整的视频数据赋值给响应（符合类型注解 List[DouyinAwemeData]）
                    sync_result.video_items = video_data_list
                    sync_result.aweme_ids = video_ids  # 保持向后兼容

                    # 智能批量处理 DouyinAweme 表记录（创建和更新）
                    try:
                        self.logger.info(f"开始智能处理 DouyinAweme 记录，作者: {user_id}")

                        # 使用服务层处理数据库操作，传入作者信息以补充视频数据

                        aweme_created, aweme_existing = await DouyinAwemeService.ensure_douyin_aweme_records(
                            video_data_list=video_data_list,
                            video_ids=video_ids,
                            author_data=sync_result.author_data,  # 传入作者信息
                        )
                        self.logger.info(f"DouyinAweme 智能处理完成 - 新创建: {aweme_created}, 已存在/更新: {aweme_existing}")

                        # 更新同步结果统计
                        sync_result.videos_synced = len(video_ids)

                    except Exception as e:
                        error_msg: str = f"DouyinAweme 表操作失败: {str(e)}"
                        sync_result.errors.append(error_msg)
                        self.logger.error(error_msg)
                        # 继续执行，不中断主流程

                    # 批量检查已存在的关联记录
                    existing_relations = await TrendInsightVideoRelated.filter(
                        source_id=user_id, video_id__in=video_ids
                    ).values_list("video_id", flat=True)

                    existing_video_ids = set(existing_relations)
                    sync_result.relations_existing = len(existing_video_ids)

                    # 准备批量创建的数据
                    new_relations: List[TrendInsightVideoRelated] = []
                    for video_id in video_ids:
                        if video_id not in existing_video_ids:
                            new_relation = TrendInsightVideoRelated(
                                source_type=SourceType.AUTHOR,
                                source_id=user_id,
                                video_id=video_id,
                                platform=Platform.DOUYIN,
                            )
                            new_relations.append(new_relation)

                    # 批量创建新关联记录
                    if new_relations:
                        await TrendInsightVideoRelated.bulk_create(new_relations)
                        sync_result.relations_created = len(new_relations)
                        self.logger.info(f"创建了 {len(new_relations)} 个新的作者视频关联记录")
                    else:
                        sync_result.relations_created = 0
                        self.logger.info(f"所有作者视频关联已存在，无需创建: {user_id}")
                else:
                    error_msg: str = (
                        f"获取 TrendInsight 用户热门视频失败: status={video_response.status}, msg={video_response.msg}"
                    )
                    sync_result.errors.append(error_msg)
                    self.logger.error(error_msg)

            except Exception as e:
                error_msg: str = f"同步用户热门视频失败: {str(e)}"
                sync_result.errors.append(error_msg)
                self.logger.error(error_msg)

            # 5. 同步完成后更新作者的实际视频计数
            try:
                # 查询 trendinsight_video_related 表，统计该作者关联的实际视频数量
                # TrendInsightVideoRelated 模型没有 platform 字段，只使用 source_type 和 source_id 过滤
                actual_video_count = await TrendInsightVideoRelated.filter(
                    source_type=SourceType.AUTHOR, source_id=user_id
                ).count()

                self.logger.info(f"作者 {user_id} 实际关联视频数量: {actual_video_count}")

                # 更新作者记录的 item_count 字段为实际同步成功的数量
                if sync_result.author_data:
                    author_record = await TrendInsightAuthor.filter(user_id=user_id).first()
                    if author_record:
                        # 更新 item_count 为实际数量
                        author_record.item_count = str(actual_video_count)
                        author_record.item_count_int = actual_video_count
                        await author_record.save()

                        # 更新响应中的作者数据
                        sync_result.author_data.item_count = str(actual_video_count)

                        self.logger.info(f"已更新作者 {user_id} 的 item_count 为实际数量: {actual_video_count}")
                    else:
                        self.logger.warning(f"警告: 未找到作者记录进行计数更新: {user_id}")

            except Exception as e:
                error_msg: str = f"更新作者视频计数失败: {str(e)}"
                sync_result.errors.append(error_msg)
                self.logger.error(error_msg)
                # 不中断主流程，继续返回结果

            # 6. 可选：处理收件箱关联（为所有订阅用户）
            if enable_inbox_relation and sync_result.video_items:
                await self._process_inbox_relation(
                    user_id, sync_result.video_items, sync_result
                )

            return sync_result

        except Exception as e:
            # 处理整个方法的异常
            error_msg = f"同步作者和视频失败: {str(e)}"
            if 'sync_result' in locals():
                sync_result.errors.append(error_msg)
                return sync_result
            else:
                # 如果 sync_result 还没有初始化，创建一个失败的响应
                return AuthorSyncResponse(
                    author_action=AuthorActionType.EXISTING,
                    author_data=None,
                    videos_synced=0,
                    videos_failed=0,
                    relations_created=0,
                    relations_existing=0,
                    aweme_ids=[],
                    video_items=[],
                    errors=[error_msg]
                )

    async def _process_inbox_relation(
        self,
        user_id: str,
        video_data_list: List[DouyinAwemeData],
        sync_result: AuthorSyncResponse
    ) -> None:
        """
        处理收件箱关联 - 为所有订阅了该作者的用户处理收件箱关联

        Args:
            user_id: 作者用户ID
            video_data_list: 视频数据列表
            sync_result: 同步结果对象
        """
        try:
            self.service_logger.log_business_event(
                "开始查找作者订阅用户",
                {
                    "user_id": user_id,
                    "video_count": len(video_data_list)
                }
            )

            # 1. 查找所有订阅了该作者的用户
            subscribed_users = await UserInboxSourceRelated.filter(
                source_id=user_id,  # 作者用户ID作为source_id
                source_type=QihaoSourceType.AUTHOR,  # 来源类型为作者
                is_deleted=False  # 未删除的订阅
            ).values_list("user_uuid", flat=True)

            # 去重用户列表
            unique_users = list(set(subscribed_users))

            if not unique_users:
                self.service_logger.log_business_event(
                    "无用户订阅该作者",
                    {"user_id": user_id}
                )
                return

            self.service_logger.log_business_event(
                "找到作者订阅用户",
                {
                    "user_id": user_id,
                    "user_count": len(unique_users),
                    "video_count": len(video_data_list)
                }
            )

            # 2. 为每个订阅用户处理收件箱关联
            processed_users = 0
            failed_users = 0

            for user_uuid in unique_users:
                try:
                    # 将 DouyinAwemeData 对象转换为字典格式，以便 InboxRelationService 处理
                    raw_video_list = []
                    for video_data in video_data_list:
                        video_dict = {
                            "aweme_id": video_data.aweme_id,
                            "publish_time": video_data.create_time.timestamp() if video_data.create_time else None,
                            "title": video_data.title,
                            "desc": video_data.desc,
                            "nickname": video_data.nickname,
                            "liked_count": video_data.liked_count,
                            "comment_count": video_data.comment_count,
                            "share_count": video_data.share_count,
                            "collected_count": video_data.collected_count
                        }
                        raw_video_list.append(video_dict)

                    # 使用 InboxRelationService 处理收件箱关联
                    inbox_result = await self.inbox_relation_service.process_user_inbox_relation(
                        user_uuid=user_uuid,
                        source_id=user_id,
                        source_type=QihaoSourceType.AUTHOR.value,
                        video_list=raw_video_list
                    )

                    relations_created = inbox_result.get("source_relations_created", 0)
                    video_relations_created = inbox_result.get("video_relations_created", 0)

                    if relations_created > 0 or video_relations_created > 0:
                        processed_users += 1
                        self.service_logger.log_business_event(
                            "用户收件箱关联处理完成",
                            {
                                "user_uuid": user_uuid,
                                "user_id": user_id,
                                "processed_count": len(video_data_list),
                                "relations_created": relations_created,
                                "video_relations_created": video_relations_created
                            }
                        )
                    else:
                        self.service_logger.log_business_event(
                            "用户收件箱关联处理无结果",
                            {
                                "user_uuid": user_uuid,
                                "user_id": user_id
                            }
                        )

                except Exception as user_error:
                    failed_users += 1
                    error_msg = f"用户 {user_uuid} 收件箱关联处理失败: {str(user_error)}"
                    sync_result.errors.append(error_msg)
                    self.error_tracker.track_error("user_inbox_relation_processing", user_error)
                    self.service_logger.log_operation_error(f"用户 {user_uuid} 收件箱关联处理", user_error)

            # 3. 记录总体处理结果
            self.service_logger.log_business_event(
                "作者订阅用户收件箱关联处理完成",
                {
                    "user_id": user_id,
                    "total_users": len(unique_users),
                    "processed_users": processed_users,
                    "failed_users": failed_users,
                    "video_count": len(video_data_list)
                }
            )

        except Exception as e:
            error_msg = f"处理作者订阅用户收件箱关联失败: {str(e)}"
            sync_result.errors.append(error_msg)
            self.error_tracker.track_error("author_inbox_relation_processing", e)
            self.service_logger.log_operation_error("处理作者订阅用户收件箱关联", e)

    async def sync_author_with_inbox_relation(
        self,
        user_id: str,
        user_uuid: Optional[str] = None,
        enable_inbox_relation: bool = True,
        enable_deduplication: bool = True,
        enable_time_filter: bool = False,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> AuthorSyncResponse:
        """
        同步作者数据并处理收件箱关联

        Args:
            user_id: 作者用户ID
            user_uuid: 用户UUID（用于收件箱关联）
            enable_inbox_relation: 是否启用收件箱关联处理
            enable_deduplication: 是否启用去重
            enable_time_filter: 是否启用时间过滤
            start_time: 开始时间（可选）
            end_time: 结束时间（可选）

        Returns:
            AuthorSyncResponse: 同步结果，包含收件箱关联信息
        """
        operation_id = self.performance_monitor.start_operation("sync_author_with_inbox_relation")

        try:
            self.service_logger.log_business_event(
                "开始作者同步（含收件箱关联）",
                {
                    "user_id": user_id,
                    "user_uuid": user_uuid,
                    "enable_inbox_relation": enable_inbox_relation,
                    "enable_deduplication": enable_deduplication,
                    "enable_time_filter": enable_time_filter
                }
            )

            # 1. 执行标准的作者同步
            sync_result = await self.sync_author_videos(user_id, enable_inbox_relation)

            # 检查同步是否成功（通过 errors 列表判断）
            if len(sync_result.errors) > 0:
                return sync_result

            # 2. 如果启用收件箱关联且提供了用户UUID，则处理收件箱关联
            if enable_inbox_relation and user_uuid:
                try:
                    # 获取同步的视频数据
                    video_data_list = sync_result.video_items

                    if video_data_list:
                        # 转换为 DouyinAweme 对象列表（用于收件箱处理）
                        from models.douyin import DouyinAweme

                        raw_video_list = []
                        for video_data in video_data_list:
                            # 创建 DouyinAweme 对象
                            aweme = DouyinAweme(
                                aweme_id=video_data.aweme_id,
                                create_time=video_data.create_time,
                                title=video_data.title,
                                desc=video_data.desc,
                                liked_count=video_data.liked_count,
                                comment_count=video_data.comment_count,
                                share_count=video_data.share_count,
                                collected_count=video_data.collected_count,
                                aweme_url=video_data.aweme_url,
                                cover_url=video_data.cover_url,
                                video_download_url=video_data.video_download_url,
                                source_keyword=video_data.source_keyword,
                                # 用户信息
                                user_id=sync_result.author_data.user_id,
                                sec_uid=sync_result.author_data.sec_uid,
                                nickname=sync_result.author_data.user_name,
                                avatar=sync_result.author_data.avatar,
                                user_signature=sync_result.author_data.user_signature
                            )
                            raw_video_list.append(aweme)

                        # 处理收件箱关联
                        inbox_result = await self.inbox_service.process_source_videos_by_params(
                            user_uuid=user_uuid,
                            source_id=user_id,
                            source_type=QihaoSourceType.AUTHOR.value,
                            raw_video_list=raw_video_list,
                            enable_deduplication=enable_deduplication,
                            enable_time_filter=enable_time_filter,
                            start_time=start_time,
                            end_time=end_time
                        )

                        # 将收件箱处理结果添加到同步结果中
                        if hasattr(sync_result, 'inbox_relation_result'):
                            sync_result.inbox_relation_result = inbox_result
                        else:
                            # 如果 AuthorSyncResponse 没有这个字段，我们添加到 metadata 中
                            if not hasattr(sync_result, 'metadata'):
                                sync_result.metadata = {}
                            sync_result.metadata['inbox_relation_result'] = inbox_result

                        self.service_logger.log_business_event(
                            "收件箱关联处理完成",
                            {
                                "user_uuid": user_uuid,
                                "source_id": user_id,
                                "video_count": len(raw_video_list),
                                "inbox_result": inbox_result
                            }
                        )

                except Exception as e:
                    error_msg = f"收件箱关联处理失败: {str(e)}"
                    sync_result.errors.append(error_msg)
                    self.error_tracker.track_error("inbox_relation_processing", e)
                    self.service_logger.log_operation_error("收件箱关联处理", e)

            self.service_logger.log_business_event(
                "作者同步（含收件箱关联）完成",
                {
                    "user_id": user_id,
                    "success": len(sync_result.errors) == 0,
                    "video_count": len(sync_result.video_items) if sync_result.video_items else 0,
                    "has_inbox_relation": enable_inbox_relation and user_uuid is not None
                }
            )

            return sync_result

        except Exception as e:
            self.error_tracker.track_error("sync_author_with_inbox_relation", e)
            raise
        finally:
            self.performance_monitor.finish_operation(operation_id)

    async def batch_sync_authors_with_inbox_relation(
        self,
        author_user_ids: List[str],
        user_uuid: Optional[str] = None,
        enable_inbox_relation: bool = True,
        batch_size: int = 5
    ) -> List[AuthorSyncResponse]:
        """
        批量同步多个作者并处理收件箱关联

        Args:
            author_user_ids: 作者用户ID列表
            user_uuid: 用户UUID（用于收件箱关联）
            enable_inbox_relation: 是否启用收件箱关联处理
            batch_size: 批处理大小

        Returns:
            List[AuthorSyncResponse]: 同步结果列表
        """
        self.validate_required_params(author_user_ids=author_user_ids)

        if not author_user_ids:
            return []

        operation_id = self.performance_monitor.start_operation("batch_sync_authors_with_inbox_relation")

        try:
            self.service_logger.log_business_event(
                "开始批量作者同步（含收件箱关联）",
                {
                    "author_count": len(author_user_ids),
                    "user_uuid": user_uuid,
                    "enable_inbox_relation": enable_inbox_relation,
                    "batch_size": batch_size
                }
            )

            results = []

            # 使用基类的批量处理方法
            async def process_batch(batch_user_ids: List[str]):
                batch_results = []
                for user_id in batch_user_ids:
                    try:
                        result = await self.sync_author_with_inbox_relation(
                            user_id=user_id,
                            user_uuid=user_uuid,
                            enable_inbox_relation=enable_inbox_relation
                        )
                        batch_results.append(result)
                    except Exception as e:
                        # 创建失败的结果对象
                        error_result = AuthorSyncResponse(
                            author_action=AuthorActionType.EXISTING,
                            author_data=None,
                            videos_synced=0,
                            videos_failed=0,
                            relations_created=0,
                            relations_existing=0,
                            aweme_ids=[],
                            video_items=[],
                            errors=[f"同步作者 {user_id} 失败: {str(e)}"]
                        )
                        batch_results.append(error_result)
                        self.error_tracker.track_error(f"sync_author_{user_id}", e)

                return batch_results

            # 执行批量处理
            batch_results = await self.batch_process(
                items=author_user_ids,
                batch_size=batch_size,
                processor=process_batch,
                delay_between_batches=0.5  # 批次间延迟0.5秒
            )

            # 展平结果
            for batch_result in batch_results:
                results.extend(batch_result)

            # 统计结果（通过 errors 列表判断成功状态）
            success_count = sum(1 for result in results if len(result.errors) == 0)
            error_count = len(results) - success_count

            self.service_logger.log_business_event(
                "批量作者同步（含收件箱关联）完成",
                {
                    "total_authors": len(author_user_ids),
                    "success_count": success_count,
                    "error_count": error_count,
                    "success_rate": round((success_count / len(results) * 100), 2) if results else 0
                }
            )

            return results

        except Exception as e:
            self.error_tracker.track_error("batch_sync_authors_with_inbox_relation", e)
            raise
        finally:
            self.performance_monitor.finish_operation(operation_id)


# 创建控制器实例
author_sync_controller = AuthorSyncController()
