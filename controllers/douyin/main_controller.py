"""
抖音主控制器

整合所有抖音相关的控制器，提供统一的接口，包括：
- HTML方式获取数据 (jingxuan, mobile)
- RPC方式获取数据
- 智能选择最佳获取方式
- 统一的错误处理和日志记录
"""

from typing import Dict, List, Optional

from loguru import logger

from mappers.douyin import FetcherConfig
from models.douyin.models import DouyinAweme

from .controller import DouyinController as DouyinVideoController
from .html.html import DouyinHTMLController
from .models import JingxuanDataFetchResult, MobileDataFetchResult, RPCDataFetchResult
# DouyinDataService 将在需要时延迟导入以避免循环依赖
from .video.video_fetcher import VideoFetcherController
from .collection.collection import DouyinCollectionController


class DouyinController:
    """抖音主控制器 - 提供统一的抖音数据获取接口"""

    def __init__(self):
        """初始化主控制器"""
        self.html_controller = DouyinHTMLController()
        self.video_controller = DouyinVideoController()
        self.video_fetcher_controller = VideoFetcherController()
        self.collection_controller = DouyinCollectionController()
        self.data_service = None  # 延迟初始化
        self.logger = logger.bind(component="DouyinController")

    def _get_data_service(self):
        """获取数据服务实例，延迟初始化以避免循环导入"""
        if self.data_service is None:
            from services.douyin import DouyinDataService
            self.data_service = DouyinDataService()
        return self.data_service

    # HTML方式获取数据的方法
    async def fetch_jingxuan_data(
        self,
        aweme_id: str,
        use_proxy: bool = True,
        custom_headers: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        save_to_db: bool = True,
    ) -> Dict:
        """
        通过精选页面获取视频数据

        Args:
            aweme_id: 抖音视频ID
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 请求超时时间
            save_to_db: 是否保存到数据库

        Returns:
            Dict: 包含success、data、source等字段的结果
        """
        self.logger.info(f"开始通过精选页面获取视频数据: {aweme_id}")

        return await self.html_controller.fetch_jingxuan_video_data(
            aweme_id=aweme_id,
            use_proxy=use_proxy,
            custom_headers=custom_headers,
            timeout=timeout,
            save_to_db=save_to_db,
        )

    async def fetch_mobile_data(
        self,
        aweme_id: str,
        use_proxy: bool = True,
        custom_headers: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        save_to_db: bool = True,
    ) -> Dict:
        """
        通过移动端分享页面获取视频数据

        Args:
            aweme_id: 抖音视频ID
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 请求超时时间
            save_to_db: 是否保存到数据库

        Returns:
            Dict: 包含success、data、source等字段的结果
        """
        self.logger.info(f"开始通过移动端分享页面获取视频数据: {aweme_id}")

        return await self.html_controller.fetch_mobile_share_video_data(
            aweme_id=aweme_id,
            use_proxy=use_proxy,
            custom_headers=custom_headers,
            timeout=timeout,
            save_to_db=save_to_db,
        )



    # 新的统一获取方法（推荐使用）
    async def fetch_video_data_unified(
        self,
        aweme_id: str,
        preferred_methods: Optional[List[str]] = None,
        config: Optional[FetcherConfig] = None,
        use_proxy: bool = True,
        save_to_db: bool = True,
    ) -> Dict:
        """
        使用新的统一视频获取器获取数据

        Args:
            aweme_id: 抖音视频ID
            preferred_methods: 首选方法列表 ["mobile", "jingxuan", "rpc"]
            config: 获取器配置，如果不提供则使用默认配置
            use_proxy: 是否使用代理
            save_to_db: 是否保存到数据库

        Returns:
            Dict: 包含success、data、source、method等字段的结果
        """
        self.logger.info(f"使用统一获取器获取视频数据: {aweme_id}")

        if config is None:
            # 创建默认配置
            config = FetcherConfig(
                preferred_methods=preferred_methods or ["mobile", "jingxuan", "rpc"],
                fallback_enabled=True,
                retry_attempts=2,
                retry_delay=1.0,
                timeout=30,
                use_proxy=use_proxy,
                save_to_db=save_to_db,
            )

        try:
            # 使用新的VideoFetcherController
            result = await self.video_fetcher_controller.fetch_video_data(aweme_id, config)

            # 转换为与现有接口兼容的格式
            return {
                "success": result.success,
                "data": result.data,
                "source": result.source,
                "method": result.method_used,
                "aweme_id": aweme_id,
                "fetch_time": result.fetch_time.isoformat() if result.fetch_time else None,
                "errors": [str(e) for e in result.errors] if result.errors else None,
            }

        except Exception as e:
            self.logger.error(f"统一获取器获取失败: {e}")
            return {
                "success": False,
                "aweme_id": aweme_id,
                "error": str(e),
                "method": "unified_fetcher",
            }

    # 批量获取方法（新版本）
    async def batch_fetch_video_data_unified(
        self,
        aweme_ids: List[str],
        config: Optional[FetcherConfig] = None,
        max_concurrent: int = 5,
    ) -> List[Dict]:
        """
        使用新的统一获取器批量获取视频数据

        Args:
            aweme_ids: 视频ID列表
            config: 获取器配置
            max_concurrent: 最大并发数

        Returns:
            List[Dict]: 结果列表
        """
        self.logger.info(f"使用统一获取器批量获取: {len(aweme_ids)}个视频")

        if config is None:
            # 创建默认配置
            config = FetcherConfig(
                preferred_methods=["mobile", "jingxuan", "rpc"],
                fallback_enabled=True,
                retry_attempts=2,
                retry_delay=1.0,
                timeout=30,
                max_concurrent=max_concurrent,
            )

        try:
            # 使用新的VideoFetcherController
            batch_result = await self.video_fetcher_controller.batch_fetch(aweme_ids, config)

            # 转换为与现有接口兼容的格式
            results = []
            for result in batch_result.results:
                results.append(
                    {
                        "success": result.success,
                        "data": result.data,
                        "source": result.source,
                        "method": result.method_used,
                        "aweme_id": result.aweme_id,
                        "fetch_time": result.fetch_time.isoformat() if result.fetch_time else None,
                        "errors": [str(e) for e in result.errors] if result.errors else None,
                    }
                )

            self.logger.info(f"统一获取器批量完成: {batch_result.success_count}/{len(aweme_ids)} 成功")
            return results

        except Exception as e:
            self.logger.error(f"统一获取器批量获取失败: {e}")
            # 返回失败结果
            return [
                {
                    "success": False,
                    "aweme_id": aweme_id,
                    "error": str(e),
                    "method": "unified_fetcher",
                }
                for aweme_id in aweme_ids
            ]

    # 智能获取方法（保留向后兼容性）
    async def fetch_video_data_auto(
        self,
        aweme_id: str,
        preferred_method: str = "jingxuan",
        fallback_methods: Optional[List[str]] = None,
        use_proxy: bool = True,
        save_to_db: bool = True,
    ) -> Dict:
        """
        智能获取视频数据，支持多种方法和回退机制

        Args:
            aweme_id: 抖音视频ID
            preferred_method: 首选方法 ("jingxuan", "mobile", "rpc")
            fallback_methods: 回退方法列表，如果首选方法失败则依次尝试
            use_proxy: 是否使用代理
            save_to_db: 是否保存到数据库

        Returns:
            Dict: 包含success、data、source、method等字段的结果
        """
        if fallback_methods is None:
            fallback_methods = ["mobile", "rpc"]

        self.logger.info(f"智能获取视频数据: {aweme_id}, 首选方法: {preferred_method}")

        # 先从数据库查询
        try:
            db_video = await DouyinAweme.filter(aweme_id=aweme_id).first()
            if db_video:
                self.logger.info(f"从数据库获取到视频数据: {aweme_id}")
                return {
                    "success": True,
                    "data": await db_video.to_dict(),
                    "source": "database",
                    "method": "database",
                    "aweme_id": aweme_id,
                }
        except Exception as e:
            self.logger.warning(f"查询数据库失败: {e}")

        # 构建方法列表
        methods = [preferred_method] + [m for m in fallback_methods if m != preferred_method]

        last_error = None

        for method in methods:
            try:
                self.logger.info(f"尝试使用方法: {method}")

                if method == "jingxuan":
                    result = await self.fetch_jingxuan_data(
                        aweme_id=aweme_id, use_proxy=use_proxy, save_to_db=save_to_db
                    )
                elif method == "mobile":
                    result = await self.fetch_mobile_data(aweme_id=aweme_id, use_proxy=use_proxy, save_to_db=save_to_db)
                elif method == "rpc":
                    rpc_result = await self.video_controller.get_video_db_auto_cookies(aweme_id)
                    if isinstance(rpc_result, dict):
                        result = {
                            "success": True,
                            "data": rpc_result,
                            "source": "rpc_api",
                            "method": method,
                            "aweme_id": aweme_id,
                        }
                    else:
                        result = {"success": False, "method": method}
                else:
                    self.logger.warning(f"不支持的方法: {method}")
                    continue

                if result.get("success", False):
                    result["method"] = method
                    self.logger.info(f"方法 {method} 获取成功: {aweme_id}")
                    return result
                else:
                    last_error = result.get("error", f"方法 {method} 失败")
                    self.logger.warning(f"方法 {method} 失败: {last_error}")

            except Exception as e:
                last_error = str(e)
                self.logger.error(f"方法 {method} 异常: {e}")
                continue

        # 所有方法都失败
        self.logger.error(f"所有方法都失败，无法获取视频数据: {aweme_id}")
        return {
            "success": False,
            "aweme_id": aweme_id,
            "error": f"所有获取方法都失败: {last_error}",
            "methods_tried": methods,
        }

    # 批量获取方法
    async def batch_fetch_video_data(
        self, aweme_ids: List[str], method: str = "auto", max_concurrent: int = 5, use_proxy: bool = True
    ) -> List[Dict]:
        """
        批量获取视频数据

        Args:
            aweme_ids: 视频ID列表
            method: 获取方法 ("auto", "jingxuan", "mobile", "rpc")
            max_concurrent: 最大并发数
            use_proxy: 是否使用代理

        Returns:
            List[Dict]: 结果列表
        """
        import asyncio
        from asyncio import Semaphore

        self.logger.info(f"开始批量获取视频数据: {len(aweme_ids)}个视频, 方法: {method}")

        # 创建信号量控制并发
        semaphore = Semaphore(max_concurrent)

        async def fetch_single_video(aweme_id: str) -> Dict:
            async with semaphore:
                try:
                    if method == "auto":
                        return await self.fetch_video_data_auto(aweme_id, use_proxy=use_proxy)
                    elif method == "jingxuan":
                        return await self.fetch_jingxuan_data(aweme_id, use_proxy=use_proxy)
                    elif method == "mobile":
                        return await self.fetch_mobile_data(aweme_id, use_proxy=use_proxy)
                    elif method == "rpc":
                        rpc_result = await self.video_controller.get_video_db_auto_cookies(aweme_id)
                        return {
                            "success": True,
                            "data": rpc_result,
                            "source": "rpc_api",
                            "method": method,
                            "aweme_id": aweme_id,
                        }
                    else:
                        return {
                            "success": False,
                            "aweme_id": aweme_id,
                            "error": f"不支持的方法: {method}",
                            "method": method,
                        }
                except Exception as e:
                    self.logger.error(f"批量获取视频 {aweme_id} 失败: {e}")
                    return {"success": False, "aweme_id": aweme_id, "error": str(e), "method": method}

        # 执行批量任务
        tasks = [fetch_single_video(aweme_id) for aweme_id in aweme_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(
                    {"success": False, "aweme_id": aweme_ids[i], "error": str(result), "method": method}
                )
            else:
                processed_results.append(result)

        success_count = sum(1 for r in processed_results if r.get("success", False))
        self.logger.info(f"批量获取完成: {success_count}/{len(aweme_ids)} 成功")

        return processed_results



    # 获取底层控制器的方法（用于高级用法）
    def get_html_controller(self) -> DouyinHTMLController:
        """获取HTML控制器实例"""
        return self.html_controller

    def get_video_controller(self) -> DouyinVideoController:
        """获取视频控制器实例"""
        return self.video_controller

    def get_video_fetcher_controller(self) -> VideoFetcherController:
        """获取新的统一视频获取器实例"""
        return self.video_fetcher_controller

    def get_collection_controller(self) -> DouyinCollectionController:
        """获取收藏夹控制器实例"""
        return self.collection_controller



    def get_data_service(self):
        """获取数据服务实例"""
        return self._get_data_service()


# 创建默认控制器实例
douyin_controller = DouyinController()
